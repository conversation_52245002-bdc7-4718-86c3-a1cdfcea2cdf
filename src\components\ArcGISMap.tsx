import React, { useEffect, useRef, useState } from 'react';
import {
  Map,
  Layers,
  Navigation,
  Search,
  Maximize2,
  Minimize2,
  MapPin,
  Satellite,
  Globe,
  Info
} from 'lucide-react';

// ArcGIS imports (will be available after npm install)
// import Map from '@arcgis/core/Map';
// import MapView from '@arcgis/core/views/MapView';
// import SceneView from '@arcgis/core/views/SceneView';
// import Basemap from '@arcgis/core/Basemap';
// import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
// import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
// import Graphic from '@arcgis/core/Graphic';
// import Point from '@arcgis/core/geometry/Point';
// import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol';

import { DigitalTwinModel } from '../App';
import { GeospatialModelManager } from './GeospatialModelManager';
import { ViewSyncControls } from './ViewSyncControls';
import { SpatialAnalysisTools } from './SpatialAnalysisTools';
import { RealTimeDataVisualization } from './RealTimeDataVisualization';
import { mapModelSyncService } from '../services/mapModelSyncService';

interface ArcGISMapProps {
  models: DigitalTwinModel[];
  selectedModel: DigitalTwinModel | null;
  onModelSelect: (model: DigitalTwinModel) => void;
  onLocationSelect: (coordinates: { latitude: number; longitude: number }) => void;
  onModelUpdate: (model: DigitalTwinModel) => void;
}

interface MapSettings {
  basemap: 'streets' | 'satellite' | 'hybrid' | 'terrain';
  viewType: '2d' | '3d';
  showModels: boolean;
  showAnalysis: boolean;
}

export const ArcGISMap: React.FC<ArcGISMapProps> = ({
  models,
  selectedModel,
  onModelSelect,
  onLocationSelect,
  onModelUpdate
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapView, setMapView] = useState<any>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [mapSettings, setMapSettings] = useState<MapSettings>({
    basemap: 'streets',
    viewType: '2d',
    showModels: true,
    showAnalysis: false
  });

  // Sync service integration
  useEffect(() => {
    const handleMapClick = (coordinates: { latitude: number; longitude: number }) => {
      const nearbyModels = mapModelSyncService.findNearbyModels(coordinates, models, 500);
      mapModelSyncService.handleLocationClick(coordinates, nearbyModels);
      onLocationSelect(coordinates);
    };

    // Simulate map click handling (replace with actual ArcGIS click handler)
    const mapElement = mapRef.current;
    if (mapElement) {
      const clickHandler = (event: MouseEvent) => {
        // This is a placeholder - in real implementation, convert screen coordinates to geo coordinates
        const rect = mapElement.getBoundingClientRect();
        const x = (event.clientX - rect.left) / rect.width;
        const y = (event.clientY - rect.top) / rect.height;

        // Convert to approximate coordinates (placeholder calculation)
        const lat = 34.0522 + (0.5 - y) * 0.1;
        const lng = -118.2437 + (x - 0.5) * 0.1;

        handleMapClick({ latitude: lat, longitude: lng });
      };

      mapElement.addEventListener('click', clickHandler);
      return () => mapElement.removeEventListener('click', clickHandler);
    }
  }, [models, onLocationSelect]);

  // Initialize ArcGIS Map
  useEffect(() => {
    if (!mapRef.current) return;

    const initializeMap = async () => {
      try {
        setIsLoading(true);

        // For now, we'll create a placeholder implementation
        // This will be replaced with actual ArcGIS implementation after package installation

        // Simulate map initialization
        setTimeout(() => {
          setIsLoading(false);
          console.log('ArcGIS Map initialized (placeholder)');
        }, 2000);

        // TODO: Replace with actual ArcGIS implementation:
        /*
        const map = new Map({
          basemap: mapSettings.basemap
        });

        const view = mapSettings.viewType === '3d' 
          ? new SceneView({
              container: mapRef.current,
              map: map,
              camera: {
                position: {
                  x: -118.80500,
                  y: 34.02700,
                  z: 50000
                },
                tilt: 65
              }
            })
          : new MapView({
              container: mapRef.current,
              map: map,
              center: [-118.80500, 34.02700],
              zoom: 13
            });

        await view.when();
        setMapView(view);
        setIsLoading(false);
        */

      } catch (error) {
        console.error('Error initializing ArcGIS Map:', error);
        setIsLoading(false);
      }
    };

    initializeMap();

    return () => {
      // Cleanup map view
      if (mapView) {
        mapView.destroy();
      }
    };
  }, [mapSettings.basemap, mapSettings.viewType]);

  // Add model markers to map
  useEffect(() => {
    if (!mapView || !models.length) return;

    // TODO: Add actual model markers to ArcGIS map
    /*
    const graphicsLayer = new GraphicsLayer();
    
    models.forEach(model => {
      if (model.coordinates) {
        const point = new Point({
          longitude: model.coordinates.longitude,
          latitude: model.coordinates.latitude
        });

        const symbol = new SimpleMarkerSymbol({
          color: selectedModel?.id === model.id ? [255, 0, 0] : [0, 100, 255],
          size: selectedModel?.id === model.id ? 12 : 8,
          outline: {
            color: [255, 255, 255],
            width: 2
          }
        });

        const graphic = new Graphic({
          geometry: point,
          symbol: symbol,
          attributes: {
            modelId: model.id,
            name: model.name
          }
        });

        graphicsLayer.add(graphic);
      }
    });

    mapView.map.add(graphicsLayer);
    */

  }, [mapView, models, selectedModel]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleBasemapChange = (basemap: MapSettings['basemap']) => {
    setMapSettings(prev => ({ ...prev, basemap }));
  };

  const toggleViewType = () => {
    setMapSettings(prev => ({
      ...prev,
      viewType: prev.viewType === '2d' ? '3d' : '2d'
    }));
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50' : 'h-full'} bg-slate-900`}>
      {/* Map Controls */}
      <div className="absolute top-4 left-4 right-4 z-10 flex justify-between items-start">
        {/* Left Controls */}
        <div className="flex flex-col space-y-2">
          {/* Basemap Selector */}
          <div className="bg-slate-800/90 backdrop-blur-sm rounded-xl p-2 border border-slate-700/50">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBasemapChange('streets')}
                className={`p-2 rounded-lg transition-colors ${mapSettings.basemap === 'streets'
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'hover:bg-slate-700/50 text-slate-300 hover:text-white'
                  }`}
                title="Streets"
              >
                <Map className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleBasemapChange('satellite')}
                className={`p-2 rounded-lg transition-colors ${mapSettings.basemap === 'satellite'
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'hover:bg-slate-700/50 text-slate-300 hover:text-white'
                  }`}
                title="Satellite"
              >
                <Satellite className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleBasemapChange('hybrid')}
                className={`p-2 rounded-lg transition-colors ${mapSettings.basemap === 'hybrid'
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'hover:bg-slate-700/50 text-slate-300 hover:text-white'
                  }`}
                title="Hybrid"
              >
                <Layers className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleBasemapChange('terrain')}
                className={`p-2 rounded-lg transition-colors ${mapSettings.basemap === 'terrain'
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'hover:bg-slate-700/50 text-slate-300 hover:text-white'
                  }`}
                title="Terrain"
              >
                <Globe className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* View Type Toggle */}
          <div className="bg-slate-800/90 backdrop-blur-sm rounded-xl p-2 border border-slate-700/50">
            <button
              onClick={toggleViewType}
              className={`p-2 rounded-lg transition-colors ${mapSettings.viewType === '3d'
                ? 'bg-green-500/20 text-green-400'
                : 'bg-blue-500/20 text-blue-400'
                }`}
              title={`Switch to ${mapSettings.viewType === '2d' ? '3D' : '2D'} View`}
            >
              <Navigation className="w-4 h-4" />
              <span className="ml-1 text-xs font-medium">
                {mapSettings.viewType.toUpperCase()}
              </span>
            </button>
          </div>
        </div>

        {/* Right Controls */}
        <div className="flex items-center space-x-2">
          {/* Model Count */}
          <div className="bg-slate-800/90 backdrop-blur-sm rounded-xl p-3 border border-slate-700/50">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-blue-400" />
              <span className="text-white text-sm font-medium">
                {models.filter(m => m.coordinates).length} Models
              </span>
            </div>
          </div>

          {/* Fullscreen Toggle */}
          <button
            onClick={toggleFullscreen}
            className="p-3 bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-700/50 hover:bg-slate-700/50 transition-colors text-slate-300 hover:text-white"
            title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Map Container */}
      <div
        ref={mapRef}
        className="w-full h-full"
        style={{ minHeight: '400px' }}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-900/90 backdrop-blur-sm">
            <div className="flex flex-col items-center space-y-4 p-8 bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-700/50">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
              <div className="text-white font-medium">Loading ArcGIS Map...</div>
              <div className="text-slate-400 text-sm">Initializing {mapSettings.viewType.toUpperCase()} view</div>
            </div>
          </div>
        )}

        {/* Placeholder Map (will be replaced by actual ArcGIS map) */}
        {!isLoading && (
          <div className="w-full h-full bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center">
            <div className="text-center p-8 bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-700/50 max-w-md">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-teal-500 rounded-xl flex items-center justify-center">
                <Map className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">ArcGIS Map Ready</h3>
              <p className="text-slate-400 mb-4">
                Map component created. Install ArcGIS packages to enable full functionality.
              </p>
              <div className="text-left bg-slate-700/50 rounded-lg p-4 text-sm">
                <div className="text-yellow-400 font-medium mb-2">Installation Required:</div>
                <code className="text-slate-300 text-xs">
                  npm install @arcgis/core @types/arcgis-js-api
                </code>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* View Sync Controls */}
      <div className="absolute top-4 right-4 z-10 w-80 mt-20">
        <ViewSyncControls
          models={models}
          selectedModel={selectedModel}
          currentView="gis"
          onModelSelect={onModelSelect}
        />
      </div>

      {/* Spatial Analysis Tools */}
      <div className="absolute top-4 right-4 z-10 w-80 mt-96">
        <SpatialAnalysisTools
          models={models}
          selectedModel={selectedModel}
          onLocationSelect={onLocationSelect}
        />
      </div>

      {/* Geospatial Model Manager */}
      <div className="absolute bottom-4 left-4 z-10 w-96 max-h-96 overflow-y-auto">
        <GeospatialModelManager
          models={models}
          selectedModel={selectedModel}
          onModelUpdate={onModelUpdate}
        />
      </div>

      {/* Real-time Data Visualization */}
      <div className="absolute top-4 left-4 z-10 w-80">
        <RealTimeDataVisualization
          models={models}
          selectedModel={selectedModel}
        />
      </div>

      {/* Model Info Panel */}
      {selectedModel && selectedModel.coordinates && (
        <div className="absolute bottom-4 right-4 z-10 bg-slate-800/90 backdrop-blur-sm rounded-xl p-4 border border-slate-700/50 w-80">
          <div className="flex items-center space-x-2 mb-3">
            <Info className="w-5 h-5 text-blue-400" />
            <h3 className="font-medium text-white">Selected Model</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">Name:</span>
              <span className="text-white">{selectedModel.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Location:</span>
              <span className="text-white text-xs">
                {selectedModel.coordinates.latitude.toFixed(6)}, {selectedModel.coordinates.longitude.toFixed(6)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Type:</span>
              <span className="text-white uppercase">{selectedModel.type}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
