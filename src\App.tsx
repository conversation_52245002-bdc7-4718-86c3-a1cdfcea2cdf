import React, { useState } from 'react';
import { Sidebar } from './components/Sidebar';
import { ModelViewer } from './components/ModelViewer';
import { Dashboard } from './components/Dashboard';
import { FileUpload } from './components/FileUpload';
import { ModelLibrary } from './components/ModelLibrary';

export interface DigitalTwinModel {
  id: string;
  name: string;
  url: string;
  type: 'gltf' | 'bim';
  uploadDate: Date;
  size: number;
  metadata?: {
    vertices?: number;
    faces?: number;
    materials?: number;
    animations?: number;
  };
}

function App() {
  const [currentView, setCurrentView] = useState<'viewer' | 'dashboard' | 'upload' | 'library'>('viewer');
  const [models, setModels] = useState<DigitalTwinModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<DigitalTwinModel | null>(null);

  const handleModelUpload = (model: DigitalTwinModel) => {
    setModels(prev => [...prev, model]);
    setSelectedModel(model);
    setCurrentView('viewer');
  };

  const handleModelSelect = (model: DigitalTwinModel) => {
    setSelectedModel(model);
    setCurrentView('viewer');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      <div className="flex h-screen">
        <Sidebar 
          currentView={currentView} 
          onViewChange={setCurrentView}
          modelCount={models.length}
        />
        
        <main className="flex-1 overflow-hidden relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-teal-500/5" />
          
          {currentView === 'viewer' ? (
            <ModelViewer model={selectedModel} />
          ) : currentView === 'dashboard' ? (
            <Dashboard models={models} selectedModel={selectedModel} />
          ) : currentView === 'upload' ? (
            <FileUpload onModelUpload={handleModelUpload} />
          ) : (
            <ModelLibrary models={models} onModelSelect={handleModelSelect} />
          )}
        </main>
      </div>
    </div>
  );
}

export default App;